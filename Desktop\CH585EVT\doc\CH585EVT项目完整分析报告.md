# CH585EVT项目完整分析报告

## 项目概述

CH585EVT是沁恒微电子（WCH）开发的基于CH585/CH584系列微控制器的评估套件开发包。CH585是一款集成BLE无线通讯、高速USB、NFC近场通信的RISC-V MCU微控制器。

### 核心技术特性

- **处理器架构**: Qingke V4C RISC-V内核
- **无线通信**: 
  - 2Mbps低功耗蓝牙BLE通讯模块
  - NFC近场通信无线接口
- **USB接口**: 
  - USB全速控制器及收发器
  - USB高速控制器及收发器（480Mbps）
- **显示接口**: 
  - 段式LCD驱动模块
  - LED点阵屏接口
- **通信接口**: 
  - 2个SPI接口
  - 4个串口（UART）
  - I2C接口
- **模拟功能**: 
  - 14路ADC
  - 触摸按键检测模块
- **其他外设**: 定时器、PWM、看门狗等

## 项目目录结构

```
CH585EVT/
├── EVT/
│   ├── EXAM/                    # 示例程序目录
│   │   ├── ADC/                # ADC示例
│   │   ├── BLE/                # 蓝牙示例
│   │   ├── FLASH/              # Flash操作示例
│   │   ├── FreeRTOS/           # FreeRTOS移植
│   │   ├── HarmonyOS/          # HarmonyOS移植
│   │   ├── I2C/                # I2C通信示例
│   │   ├── IAP/                # 在线升级示例
│   │   ├── LCD/                # LCD显示示例
│   │   ├── LED/                # LED控制示例
│   │   ├── NFCA/               # NFC应用示例
│   │   ├── PM/                 # 电源管理示例
│   │   ├── PWMX/               # PWM示例
│   │   ├── RT-Thread/          # RT-Thread移植
│   │   ├── SPI/                # SPI通信示例
│   │   ├── SRC/                # 源码库
│   │   ├── TMR/                # 定时器示例
│   │   ├── TOUCH/              # 触摸按键示例
│   │   ├── UART1/              # 串口通信示例
│   │   └── USB/                # USB通信示例
│   └── PUB/                    # 公共文档
│       ├── CH585SCH.pdf        # 原理图
│       ├── CH585评估板说明书.pdf
│       ├── SCHPCB/             # 原理图和PCB文件
│       └── 蓝牙芯片的电路及PCB设计的重要注意事项.pdf
```

## 主要技术文档分析

### 1. CH585评估板说明书
- **版本**: 1B
- **页数**: 10页
- **主要内容**:
  - 评估板硬件介绍和功能说明
  - 天线设计指南（2.4GHz PCB天线）
  - 软件开发环境配置（MounRiver Studio）
  - 程序下载方式（串口下载、USB下载）
  - 蓝牙Peripheral例程演示

### 2. 沁恒低功耗蓝牙软件开发参考手册
- **版本**: V1.8
- **页数**: 70页
- **主要内容**:
  - TMOS任务管理系统详解
  - GAP（通用访问配置文件）和GATT（通用属性配置文件）
  - 蓝牙协议栈架构和API接口
  - 外围设备角色和中心设备角色开发
  - 配对绑定管理机制

### 3. CH585 NFC读卡器应用指南
- **版本**: V1.3
- **页数**: 10页
- **主要内容**:
  - ISO14443A兼容的NFC PCD设计
  - 低通滤波器设计
  - 天线设计和匹配网络设计
  - 接收电路设计
  - NFC开发流程指导

### 4. WCH触摸库使用说明
- **页数**: 11页
- **主要内容**:
  - 支持CH57x、CH58x、CH59x系列MCU
  - 多种滤波模式（1、3、5、7、9、CS10）
  - CS10V动态测试支持
  - 低功耗触摸检测方案
  - 防水触摸和密集模式设计

### 5. I2C接口使用指南
- **页数**: 7页
- **主要内容**:
  - I2C主模式发送和接收
  - I2C从模式操作
  - 库函数API详解
  - 时序图和事件处理

### 6. 蓝牙芯片PCB设计注意事项
- **主要内容**:
  - 天线设计要求和接地处理
  - 不同板厚的天线模板
  - 射频电路布局建议
  - EMC设计考虑

### 7. WCH蓝牙空中升级（BLE OTA）
- **版本**: v1.2
- **页数**: 3页
- **主要内容**:
  - 三种DFU升级方式
  - 后台式和非后台式升级模式
  - OTA升级工作原理和实现

## 开发环境和工具链

### 编译器
- **MounRiver Studio**: 官方推荐的集成开发环境
- **工具链**: RISC-V GCC交叉编译工具链
- **调试器**: WCH-Link调试器

### 程序下载工具
- **WCHISPTool**: 官方ISP下载工具
- **下载方式**:
  - 串口下载：UART1(PA8/PA9)、UART0(PA14/PA15)
  - USB下载：USB全速口（FS）
  - 下载引脚：PB22（下载boot脚）

## 支持的操作系统

1. **裸机开发**: 基础的无操作系统开发
2. **FreeRTOS**: FreeRTOS-KernelV10.5.1移植
3. **HarmonyOS**: kernel_liteos_m(Ver:OpenHarmony-3.2.3-Release)移植
4. **RT-Thread**: RT-Thread-Nano移植

## 主要应用领域

### 蓝牙应用
- BLE外围设备和中心设备
- 蓝牙Mesh网络
- HID设备（键盘、鼠标）
- 蓝牙音频应用
- IoT网关应用

### NFC应用
- NFC读卡器（PCD）
- NFC卡片模拟（PICC）
- Type2标签应用
- M1卡应用

### USB应用
- USB Device和Host功能
- 高速USB通信（480Mbps）
- USB HID设备

## 重要配置参数

### 系统时钟
- **时钟源**: HSE PLL 62.4MHz
- **低功耗**: 支持DCDC电源管理

### 蓝牙配置
- **MAC地址**: 可配置
- **发射功率**: 可调节
- **连接间隔**: 可配置
- **广播间隔**: 可配置

### NFC配置
- **工作频率**: 13.56MHz
- **通信协议**: ISO14443A
- **天线匹配**: 需要匹配网络设计

## 使用说明和注意事项

### 硬件注意事项
1. **天线设计**: 必须有接地端，不同板厚需要不同天线参数
2. **射频布局**: 远离干扰源（晶体、功率器件、开关电源）
3. **电源设计**: 建议使用DCDC提高效率
4. **调试接口**: 预留WCH-Link调试接口

### 软件开发注意事项
1. **中断处理**: 建议使用统一中断入口
2. **内存管理**: 注意RAM空间分配，预留栈空间
3. **低功耗**: 正确配置睡眠模式和唤醒源
4. **蓝牙开发**: 遵循TMOS任务管理机制

### 调试和测试
1. **串口调试**: 默认使用UART0，波特率115200
2. **蓝牙测试**: 使用官方提供的手机APP
3. **NFC测试**: 需要专业的NFC测试设备
4. **性能测试**: 注意射频性能和功耗测试

## 技术支持

- **官方网站**: https://wch.cn
- **技术文档**: 完整的PDF技术手册
- **示例代码**: 丰富的EXAM示例程序
- **开发工具**: MounRiver Studio和WCHISPTool

## 总结

CH585EVT是一个功能完整、文档齐全的微控制器开发套件，特别适合需要BLE、NFC、USB等多种通信功能的IoT应用开发。项目提供了从硬件设计到软件开发的完整解决方案，支持多种操作系统，具有良好的可扩展性和实用性。
